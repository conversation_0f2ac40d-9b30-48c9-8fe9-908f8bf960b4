<?php
#region region DOCS
/** @var CategoriaTransaccion[] $categs */
/** @var Transaccion $newtransaccion */
/** @var Budget[] $budgets */
/** @var string[] $unique_notas */
/** @var Transaccion[] $recent_transacciones */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Transacciones</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <link href="<?php echo RUTA ?>resources/assets/plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
    <link href="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
	<?php #region region CSS grouped controls ?>
	<link href="<?php echo RUTA ?>resources/css/grouped_controls.css" rel="stylesheet" />
	<?php #endregion CSS grouped controls ?>
	<?php #region region CSS select choices.js  ?>
	<link rel="stylesheet" href="<?php echo RUTA ?>resources/choices.js/choices.min.css">
	<link rel="stylesheet" href="<?php echo RUTA ?>resources/choices.js/fab_choices.css">
	<?php #endregion CSS select choices.js  ?>
    <?php #endregion head ?>

</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <h1 class="page-header">Transacciones</h1>
        <!-- END page-header -->

        <?php #region region FORM ?>
        <form action="itransaccion" method="POST">
            <!-- BEGIN row -->
            <div class="row mt-3">
	            <div class="container">
		            <div class="btn-group btn-group-toggle" data-toggle="buttons">
			            <?php foreach ($budgets as $budget): ?>
			            <label class="btn btn-secondary">
				            <input type="radio" name="id_budget" autocomplete="off" value="<?php echo $budget->id_budget; ?>"> <?php echo $budget->canal; ?>
			            </label>
			            <?php endforeach; ?>
		            </div>
	            </div>
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
	            <div class="container">
		            <div class="btn-group btn-group-toggle" data-toggle="buttons">
			            <label class="btn btn-secondary">
				            <input type="radio" name="tipo_transaccion" autocomplete="off" value="INGRESO"> Ingreso
			            </label>
			            <label class="btn btn-secondary">
				            <input type="radio" name="tipo_transaccion" autocomplete="off" value="EGRESO"> Egreso
			            </label>
		            </div>
	            </div>
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
	            <!-- BEGIN date fecha -->
	            <div class="col-md-3 col-xs-12">
		            <div class="mb-3">
			            <label for="fecha" class="form-label">Fecha:</label>
			            <div class="input-group">
				            <input type="text" id="fecha" name="fecha" value="<?php echo @recover_var($newtransaccion->fecha) ?>" class="form-control datepicker" autocomplete="off"/>
				            <span class="input-group-text">
                            <i class="fa fa-calendar-alt"></i>
                        </span>
			            </div>
		            </div>
	            </div>
	            <!-- END date -->

                <!-- BEGIN select -->
	            <div class="col-md-3 col-xs-12">
		            <div class="mb-3">
			            <label for="categorias" class="form-label">Categorias:</label>
			            <select id="categorias" name="id_categoriatransaccion" class="form-select">
				            <option value="">-- Seleccione Categoria --</option>
				            <?php foreach ($categs as $categ): ?>
					            <option value="<?php echo limpiar_datos($categ->id); ?>">
						            <?php echo $categ->nombre; ?>
					            </option>
				            <?php endforeach; ?>
			            </select>
		            </div>
	            </div>
                <!-- END select -->

	            <!-- BEGIN text valor -->
	            <div class="col-md-3 col-xs-12">
		            <div class="mb-3">
			            <label for="valor" class="form-label">Valor:</label>
			            <input type="text" name="valor" id="valor" value="<?php echo @recover_var($newtransaccion->valor) ?>" class="form-control" onclick="this.focus();this.select('')"/>
		            </div>
	            </div>
	            <!-- END text valor -->

                <!-- BEGIN text -->
	            <div class="col-md-3 col-xs-12">
		            <div class="mb-3">
			            <label for="nota" class="form-label">Nota:</label>
			            <input type="text" name="nota" id="nota" value="<?php echo @recover_var($newtransaccion->nota) ?>" class="form-control"/>
		            </div>
	            </div>
                <!-- END text -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
	        <div class="row mt-3">
		        <?php #region region SUBMIT sub_add ?>
		        <div class="col-md-6 col-xs-12">
			        <button type="submit" id="sub_add" name="sub_add" class="btn btn-success w-100">
				        Agregar y Regresar a la Gráfica
			        </button>
		        </div>
		        <?php #endregion sub_add ?>
		        <?php #region region SUBMIT sub_add_continue ?>
		        <div class="col-md-6 col-xs-12">
			        <button type="button" id="sub_add_continue" name="sub_add_continue" class="btn btn-primary w-100">
				        Agregar y Continuar
			        </button>
		        </div>
		        <?php #endregion sub_add_continue ?>
	        </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN link -->
                <div class="col-md-12 col-xs-12">
                    <a href="gtransacciones" class="btn btn-default w-100">
                        Regresar
                    </a>
                </div>
                <!-- END link -->
            </div>
            <!-- END row -->
        </form>
        <?php #endregion form ?>

        <?php #region region PANEL recent transactions ?>
        <div class="panel panel-inverse mt-3">
            <div class="panel-heading">
                <h4 class="panel-title d-flex justify-content-between align-items-center">
                    <span>Transacciones Recientes:</span>
                    <div>
                        <a href="ltransacciones" class="btn btn-sm btn-primary">
                            <i class="fa fa-list me-1"></i> Ver Todas
                        </a>
                    </div>
                </h4>
            </div>
            <!-- BEGIN panel-body -->
            <div class="table-nowrap" style="overflow: auto">
                <?php #region region TABLE recent transactions ?>
                <table class="table table-hover table-sm">
                    <thead>
                    <tr>
                        <th class="text-center">Valor</th>
                        <th class="text-center">Fecha</th>
                        <th class="text-center">Categorias</th>
                        <th class="text-center">Nota</th>
                        <th class="text-center">Canal</th>
                    </tr>
                    </thead>
                    <tbody class="fs-12px">
                    <?php #region region ARRAY recent transactions ?>
                    <?php if (!empty($recent_transacciones)): ?>
                        <?php foreach ($recent_transacciones as $transaccion): ?>
                            <tr>
                                <td class="text-end <?php echo ($transaccion->valor < 0) ? "text-danger" : "text-success"; ?>">
                                    <?php echo formatCurrencyConSigno($transaccion->valor); ?>
                                </td>
                                <td class="text-center"><?php echo formatDateWithSpanishDay($transaccion->fecha); ?></td>
                                <td><?php echo $transaccion->categoriasstring; ?></td>
                                <td><?php echo $transaccion->nota; ?></td>
                                <td class="text-center"><?php echo $transaccion->budget->canal; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="5" class="text-center text-muted">No hay transacciones recientes</td>
                        </tr>
                    <?php endif; ?>
                    <?php #endregion array recent transactions ?>
                    </tbody>
                </table>
                <?php #endregion table recent transactions ?>
            </div>
            <!-- END panel-body -->
        </div>
        <?php #endregion panel recent transactions ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<?php #region region JS date ?>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/js/datepickerini.js"></script>
<?php #endregion datejs ?>
<?php #region region JS grouped controls ?>
<script src="<?php echo RUTA ?>resources/js/grouped_controls.js"></script>
<?php #endregion JS grouped controls ?>
<?php #region region JS select choices.js ?>
<script src="<?php echo RUTA ?>resources/choices.js/choices.min.js"></script>
<script>
    let categoriaChoices; // Declare globally for access in clear function

    document.addEventListener('DOMContentLoaded', function () {
        const categoriasSelect = document.getElementById('categorias');
        if (categoriasSelect) {
            categoriaChoices = new Choices(categoriasSelect, {
                searchEnabled: true,
                shouldSort: false,
                placeholder: true
            });
        }
    });
</script>
<script type="text/javascript">
    $(function() {
        const availableNotes = <?php echo json_encode($unique_notas ?? []); ?>; // Ensure $unique_notas is defined, default to empty array
        $("#nota").autocomplete({
            source: availableNotes,
            minLength: 3 // Start searching after 3 character
        });
    });
</script>
<?php #endregion JS select choices.js ?>

<?php #region region JS Agregar y Continuar ?>
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        const addContinueBtn = document.getElementById('sub_add_continue');
        const form = document.querySelector('form[action="itransaccion"]');

        if (addContinueBtn && form) {
            addContinueBtn.addEventListener('click', function(e) {
                e.preventDefault();

                // Validate required fields
                const requiredFields = ['id_budget', 'tipo_transaccion', 'fecha', 'id_categoriatransaccion', 'valor'];
                let isValid = true;
                let errorMessage = '';

                // Check budget selection
                const budgetRadios = document.querySelectorAll('input[name="id_budget"]:checked');
                if (budgetRadios.length === 0) {
                    isValid = false;
                    errorMessage += 'Debe seleccionar un canal.\n';
                }

                // Check transaction type
                const tipoRadios = document.querySelectorAll('input[name="tipo_transaccion"]:checked');
                if (tipoRadios.length === 0) {
                    isValid = false;
                    errorMessage += 'Debe seleccionar el tipo de transacción.\n';
                }

                // Check other required fields
                const fecha = document.getElementById('fecha').value.trim();
                const categoria = document.getElementById('categorias').value.trim();
                const valor = document.getElementById('valor').value.trim();

                if (!fecha) {
                    isValid = false;
                    errorMessage += 'Debe especificar la fecha.\n';
                }

                if (!categoria) {
                    isValid = false;
                    errorMessage += 'Debe seleccionar una categoría.\n';
                }

                if (!valor) {
                    isValid = false;
                    errorMessage += 'Debe especificar el valor.\n';
                }

                if (!isValid) {
                    alert('Por favor complete los siguientes campos:\n\n' + errorMessage);
                    return;
                }

                // Disable button to prevent double submission
                addContinueBtn.disabled = true;
                addContinueBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Guardando...';

                // Prepare form data
                const formData = new FormData(form);
                formData.append('sub_add_continue', '1');

                // Send AJAX request
                fetch('itransaccion', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Clear form fields
                        clearForm();

                        // Show success message
                        showSuccessMessage('Transacción agregada exitosamente. Puede agregar otra.');

                        // Update recent transactions table if it exists
                        if (data.recent_transactions) {
                            updateRecentTransactionsTable(data.recent_transactions);
                        }

                        // Focus on first field
                        const firstBudgetRadio = document.querySelector('input[name="id_budget"]');
                        if (firstBudgetRadio) {
                            firstBudgetRadio.focus();
                        }
                    } else {
                        alert('Error: ' + (data.message || 'Error desconocido'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error al procesar la solicitud. Por favor intente nuevamente.');
                })
                .finally(() => {
                    // Re-enable button
                    addContinueBtn.disabled = false;
                    addContinueBtn.innerHTML = 'Agregar y Continuar';
                });
            });
        }

        function clearForm() {
            // Clear radio buttons
            document.querySelectorAll('input[name="id_budget"]').forEach(radio => radio.checked = false);
            document.querySelectorAll('input[name="tipo_transaccion"]').forEach(radio => radio.checked = false);

            // Clear text inputs
            document.getElementById('fecha').value = '<?php echo create_date(); ?>';
            document.getElementById('valor').value = '';
            document.getElementById('nota').value = '';

            // Reset category select
            const categoriaSelect = document.getElementById('categorias');
            if (categoriaSelect) {
                categoriaSelect.value = '';
                // If using Choices.js, reset it
                if (typeof categoriaChoices !== 'undefined' && categoriaChoices) {
                    categoriaChoices.setChoiceByValue('');
                }
            }

            // Remove active classes from button groups
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
        }

        function showSuccessMessage(message) {
            // Create a temporary success alert
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
            alertDiv.innerHTML = `
                <i class="fa fa-check-circle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // Insert after the page header
            const pageHeader = document.querySelector('.page-header');
            if (pageHeader) {
                pageHeader.parentNode.insertBefore(alertDiv, pageHeader.nextSibling);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        }

        function updateRecentTransactionsTable(transactions) {
            const tbody = document.querySelector('.panel-inverse tbody');
            if (!tbody) return;

            // Clear existing rows
            tbody.innerHTML = '';

            if (transactions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No hay transacciones recientes</td></tr>';
                return;
            }

            // Add new rows
            transactions.forEach(transaction => {
                const row = document.createElement('tr');
                const valorClass = transaction.valor < 0 ? 'text-danger' : 'text-success';

                row.innerHTML = `
                    <td class="text-end ${valorClass}">${transaction.valor_formatted}</td>
                    <td class="text-center">${transaction.fecha_formatted}</td>
                    <td>${transaction.categoriasstring}</td>
                    <td>${transaction.nota}</td>
                    <td class="text-center">${transaction.canal}</td>
                `;

                tbody.appendChild(row);
            });
        }
    });
</script>
<?php #endregion JS Agregar y Continuar ?>
<?php #endregion js ?>

</body>
</html>